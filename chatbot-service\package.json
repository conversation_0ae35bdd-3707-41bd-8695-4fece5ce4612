{"name": "atma-chatbot-service", "version": "1.0.0", "description": "Chatbot Service for ATMA Backend - Core conversation management and AI integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "health": "node scripts/health-check.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "ioredis": "^5.3.2", "joi": "^17.9.1", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "pg": "^8.10.0", "sequelize": "^6.31.0", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"axios": "^1.10.0", "eslint": "^8.39.0", "jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "keywords": ["chatbot", "conversation", "ai", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT", "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}}