const { Conversation, Message, UsageTracking } = require('../models');
const OpenRouterService = require('../services/openrouterService');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

// Initialize OpenRouter service
const openrouterService = new OpenRouterService();

/**
 * Send a message and get AI response
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const sendMessage = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { content, content_type = 'text', parent_message_id } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_MESSAGE_CONTENT',
          message: 'Message content cannot be empty'
        }
      });
    }

    // Validate conversation access
    const conversation = await Conversation.findOne({
      where: { 
        id: conversationId, 
        user_id: userId,
        status: 'active'
      }
    });

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found or not accessible'
        }
      });
    }

    // Save user message
    const userMessage = await Message.create({
      conversation_id: conversationId,
      sender_type: 'user',
      content: content.trim(),
      content_type: content_type,
      parent_message_id: parent_message_id
    });

    logger.info('User message saved', {
      messageId: userMessage.id,
      conversationId,
      userId,
      contentLength: content.length
    });

    // Build conversation context
    const conversationHistory = await buildConversationHistory(conversationId);
    
    // Generate AI response
    const startTime = Date.now();
    const aiResponse = await openrouterService.generateResponse(
      conversationHistory,
      { 
        userId: userId,
        conversationId: conversationId
      }
    );

    // Save assistant message
    const assistantMessage = await Message.create({
      conversation_id: conversationId,
      sender_type: 'assistant',
      content: aiResponse.content,
      metadata: {
        model: aiResponse.model,
        finish_reason: aiResponse.finishReason,
        processing_time: aiResponse.processingTime,
        request_id: aiResponse.metadata?.requestId,
        provider: aiResponse.metadata?.provider
      }
    });

    // Track usage
    await UsageTracking.create({
      conversation_id: conversationId,
      message_id: assistantMessage.id,
      model_used: aiResponse.model,
      prompt_tokens: aiResponse.usage.prompt_tokens,
      completion_tokens: aiResponse.usage.completion_tokens,
      total_tokens: aiResponse.usage.total_tokens,
      cost_credits: aiResponse.usage.cost,
      is_free_model: aiResponse.usage.isFreeModel,
      processing_time_ms: aiResponse.processingTime
    });

    // Update conversation timestamp
    await conversation.update({ updated_at: new Date() });

    logger.info('AI response generated and saved', {
      messageId: assistantMessage.id,
      conversationId,
      model: aiResponse.model,
      processingTime: aiResponse.processingTime,
      tokenUsage: aiResponse.usage
    });

    res.json({
      success: true,
      data: {
        user_message: userMessage,
        assistant_message: assistantMessage,
        usage: aiResponse.usage,
        processing_time: aiResponse.processingTime
      }
    });

  } catch (error) {
    logger.error('Error sending message:', {
      error: error.message,
      stack: error.stack,
      conversationId: req.params.conversationId,
      userId: req.user?.id
    });

    // Handle specific OpenRouter errors
    if (error.message.includes('rate limit')) {
      return res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Rate limit exceeded. Please wait before sending another message.',
          retryAfter: 60
        }
      });
    }

    if (error.message.includes('All OpenRouter models failed')) {
      return res.status(503).json({
        success: false,
        error: {
          code: 'AI_SERVICE_UNAVAILABLE',
          message: 'AI service is temporarily unavailable. Please try again later.'
        }
      });
    }

    next(error);
  }
};

/**
 * Get messages for a conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getMessages = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;
    const { 
      page = 1, 
      limit = 50, 
      include_usage = false,
      since_message_id 
    } = req.query;

    // Validate conversation access
    const conversation = await Conversation.findOne({
      where: { 
        id: conversationId, 
        user_id: userId,
        status: ['active', 'archived']
      }
    });

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found'
        }
      });
    }

    const offset = (page - 1) * limit;
    const whereClause = { conversation_id: conversationId };

    // Filter messages since a specific message ID
    if (since_message_id) {
      const sinceMessage = await Message.findByPk(since_message_id);
      if (sinceMessage) {
        whereClause.created_at = {
          [require('sequelize').Op.gt]: sinceMessage.created_at
        };
      }
    }

    const includeOptions = [];
    if (include_usage === 'true') {
      includeOptions.push({
        model: UsageTracking,
        as: 'usage_tracking',
        required: false
      });
    }

    const { count, rows: messages } = await Message.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: includeOptions
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        messages,
        conversation_id: conversationId,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    });

  } catch (error) {
    next(error);
  }
};

/**
 * Regenerate AI response for a message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const regenerateResponse = async (req, res, next) => {
  try {
    const { conversationId, messageId } = req.params;
    const userId = req.user.id;
    const { model } = req.body; // Optional: specify different model

    // Validate conversation access
    const conversation = await Conversation.findOne({
      where: { 
        id: conversationId, 
        user_id: userId,
        status: 'active'
      }
    });

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found'
        }
      });
    }

    // Find the message to regenerate (must be assistant message)
    const messageToRegenerate = await Message.findOne({
      where: {
        id: messageId,
        conversation_id: conversationId,
        sender_type: 'assistant'
      }
    });

    if (!messageToRegenerate) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'MESSAGE_NOT_FOUND',
          message: 'Assistant message not found'
        }
      });
    }

    // Build conversation history up to the point before this message
    const conversationHistory = await buildConversationHistoryBefore(
      conversationId, 
      messageToRegenerate.created_at
    );

    // Generate new AI response
    const aiResponse = await openrouterService.generateResponse(
      conversationHistory,
      { 
        userId: userId,
        conversationId: conversationId,
        model: model // Use specified model if provided
      }
    );

    // Create new assistant message
    const newAssistantMessage = await Message.create({
      conversation_id: conversationId,
      sender_type: 'assistant',
      content: aiResponse.content,
      parent_message_id: messageToRegenerate.parent_message_id,
      metadata: {
        model: aiResponse.model,
        finish_reason: aiResponse.finishReason,
        processing_time: aiResponse.processingTime,
        request_id: aiResponse.metadata?.requestId,
        provider: aiResponse.metadata?.provider,
        regenerated_from: messageId
      }
    });

    // Track usage for new message
    await UsageTracking.create({
      conversation_id: conversationId,
      message_id: newAssistantMessage.id,
      model_used: aiResponse.model,
      prompt_tokens: aiResponse.usage.prompt_tokens,
      completion_tokens: aiResponse.usage.completion_tokens,
      total_tokens: aiResponse.usage.total_tokens,
      cost_credits: aiResponse.usage.cost,
      is_free_model: aiResponse.usage.isFreeModel,
      processing_time_ms: aiResponse.processingTime
    });

    logger.info('AI response regenerated', {
      originalMessageId: messageId,
      newMessageId: newAssistantMessage.id,
      conversationId,
      model: aiResponse.model
    });

    res.json({
      success: true,
      data: {
        original_message_id: messageId,
        new_message: newAssistantMessage,
        usage: aiResponse.usage,
        processing_time: aiResponse.processingTime
      }
    });

  } catch (error) {
    logger.error('Error regenerating response:', {
      error: error.message,
      conversationId: req.params.conversationId,
      messageId: req.params.messageId,
      userId: req.user?.id
    });
    next(error);
  }
};

/**
 * Build conversation history for AI context
 * @param {string} conversationId - Conversation ID
 * @param {number} limit - Maximum number of messages to include
 * @returns {Array} Formatted conversation history
 */
async function buildConversationHistory(conversationId, limit = 20) {
  const messages = await Message.findAll({
    where: { conversation_id: conversationId },
    order: [['created_at', 'ASC']],
    limit: limit
  });

  const history = messages.map(msg => ({
    role: msg.sender_type === 'user' ? 'user' : 'assistant',
    content: msg.content
  }));

  // Optimize for token limits
  return openrouterService.optimizeConversationHistory(history);
}

/**
 * Build conversation history before a specific timestamp
 * @param {string} conversationId - Conversation ID
 * @param {Date} beforeTimestamp - Timestamp to filter before
 * @returns {Array} Formatted conversation history
 */
async function buildConversationHistoryBefore(conversationId, beforeTimestamp) {
  const messages = await Message.findAll({
    where: { 
      conversation_id: conversationId,
      created_at: {
        [require('sequelize').Op.lt]: beforeTimestamp
      }
    },
    order: [['created_at', 'ASC']],
    limit: 20
  });

  const history = messages.map(msg => ({
    role: msg.sender_type === 'user' ? 'user' : 'assistant',
    content: msg.content
  }));

  return openrouterService.optimizeConversationHistory(history);
}

module.exports = {
  sendMessage,
  getMessages,
  regenerateResponse
};
