// Test setup file
require('dotenv').config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DB_NAME = 'atma_test_db';
process.env.JWT_SECRET = 'test_jwt_secret';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// OpenRouter test configuration
process.env.OPENROUTER_API_KEY = 'test-openrouter-api-key';
process.env.OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
process.env.DEFAULT_MODEL = 'qwen/qwen-2.5-coder-32b-instruct:free';
process.env.FALLBACK_MODEL = 'meta-llama/llama-3.2-3b-instruct:free';
process.env.EMERGENCY_FALLBACK_MODEL = 'openai/gpt-4o-mini';
process.env.USE_FREE_MODELS_ONLY = 'true';
process.env.MAX_TOKENS = '1000';
process.env.TEMPERATURE = '0.7';
process.env.MAX_MESSAGE_LENGTH = '10000';

// Rate limiting test values
process.env.FREE_MODEL_RATE_WINDOW_MS = '60000';
process.env.FREE_MODEL_RATE_LIMIT_PER_MINUTE = '5';
process.env.MESSAGE_RATE_WINDOW_MS = '300000';
process.env.MESSAGE_RATE_LIMIT_PER_5MIN = '10';
process.env.BURST_PROTECTION_WINDOW_MS = '10000';
process.env.BURST_PROTECTION_LIMIT = '3';

// Mock external dependencies
jest.mock('../src/middleware/metrics', () => ({
  collectHttpMetrics: jest.fn((req, res, next) => next()),
  incrementConversationMetric: jest.fn(),
  incrementMessageMetric: jest.fn(),
  getMetrics: jest.fn(() => ({
    requests: { total: 0 },
    conversations: { created: 0 },
    messages: { sent: 0 },
    errors: { total: 0 },
    responseTime: { average: 0 }
  })),
  startMetricsLogging: jest.fn()
}));

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: '550e8400-e29b-41d4-a716-446655440000',
    email: '<EMAIL>',
    user_type: 'user'
  }),
  
  createMockConversation: () => ({
    id: '660e8400-e29b-41d4-a716-446655440001',
    user_id: '550e8400-e29b-41d4-a716-446655440000',
    title: 'Test Conversation',
    context_type: 'general',
    status: 'active',
    created_at: new Date(),
    updated_at: new Date()
  }),
  
  createMockMessage: () => ({
    id: '770e8400-e29b-41d4-a716-446655440002',
    conversation_id: '660e8400-e29b-41d4-a716-446655440001',
    sender_type: 'user',
    content: 'Test message',
    content_type: 'text',
    created_at: new Date()
  })
};

// Setup and teardown
beforeAll(async () => {
  // Any global setup
});

afterAll(async () => {
  // Any global cleanup
});

beforeEach(() => {
  // Reset mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Cleanup after each test
});
