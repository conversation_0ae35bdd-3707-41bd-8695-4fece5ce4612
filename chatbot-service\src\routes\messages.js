const express = require('express');
const router = express.Router({ mergeParams: true }); // mergeParams to access conversationId from parent route

const messageController = require('../controllers/messageController');
const auth = require('../middleware/auth');
const { validateMessage, validateRegenerateRequest } = require('../middleware/validation');
const { 
  freeModelLimiter, 
  messageRateLimiter, 
  burstProtectionLimiter,
  rateLimitLogger 
} = require('../middleware/freeModelRateLimiter');

// Apply authentication to all message routes
router.use(auth);

// Apply rate limiting and logging
router.use(rateLimitLogger);
router.use(burstProtectionLimiter); // Most restrictive first
router.use(freeModelLimiter);

/**
 * @route   POST /conversations/:conversationId/messages
 * @desc    Send a message and get AI response
 * @access  Private
 * @rateLimit Multiple layers:
 *          - Burst protection: 5 requests per 10 seconds
 *          - Free model: 20 requests per minute
 *          - Message specific: 50 requests per 5 minutes
 */
router.post('/', 
  messageRateLimiter,
  validateMessage,
  messageController.sendMessage
);

/**
 * @route   GET /conversations/:conversationId/messages
 * @desc    Get messages for a conversation
 * @access  Private
 * @rateLimit Only burst protection and free model limits (no message limit for GET)
 */
router.get('/', 
  messageController.getMessages
);

/**
 * @route   POST /conversations/:conversationId/messages/:messageId/regenerate
 * @desc    Regenerate AI response for a specific message
 * @access  Private
 * @rateLimit Same as sending messages
 */
router.post('/:messageId/regenerate',
  messageRateLimiter,
  validateRegenerateRequest,
  messageController.regenerateResponse
);

module.exports = router;
