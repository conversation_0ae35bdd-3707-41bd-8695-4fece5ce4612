# This is a template for your environment variables.
# Copy this file to .env and fill in the actual values.
# Do NOT commit the .env file with your actual secrets to version control.

# --- PostgreSQL Database ---
POSTGRES_DB=atma_db
POSTGRES_USER=atma_user
POSTGRES_PASSWORD=password_goes_here

# --- RabbitMQ Message Broker ---
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=password_goes_here

# --- Redis Cache ---
REDIS_PASSWORD=password_goes_here

# --- Application Secrets ---
# Use a long, random string for JWT_SECRET (e.g., from a password generator)
JWT_SECRET=JWT_SECRET_GOES_HERE
INTERNAL_SERVICE_KEY=INTERNAL_SERVICE_KEY_GOES_HERE
AUDIT_ENCRYPTION_KEY=AUDIT_ENCRYPTION_KEY_GOES_HERE

# --- Google AI ---
# Your actual Google AI API key
GOOGLE_AI_API_KEY=GOOGLE_AI_API_KEY_GOES_HERE

# --- Feature Flags ---
# Set to 'true' to use the mock AI model for testing/development,
# or 'false' to use the real Google AI model in production.
USE_MOCK_MODEL=false

# --- Cloudflare Tunnel ---
# Your Cloudflare Tunnel token for exposing the service
CLOUDFLARE_TUNNEL_TOKEN=CLOUDFLARE_TUNNEL_TOKEN_GOES_HERE

