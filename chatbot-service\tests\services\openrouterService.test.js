const OpenRouterService = require('../../src/services/openrouterService');
const axios = require('axios');

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock logger
jest.mock('../../src/utils/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

describe('OpenRouterService', () => {
  let openrouterService;
  let mockAxiosInstance;

  beforeEach(() => {
    // Reset environment variables
    process.env.OPENROUTER_API_KEY = 'test-api-key';
    process.env.OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
    process.env.DEFAULT_MODEL = 'qwen/qwen-2.5-coder-32b-instruct:free';
    process.env.FALLBACK_MODEL = 'meta-llama/llama-3.2-3b-instruct:free';
    process.env.EMERGENCY_FALLBACK_MODEL = 'openai/gpt-4o-mini';
    process.env.USE_FREE_MODELS_ONLY = 'true';
    process.env.MAX_TOKENS = '1000';
    process.env.TEMPERATURE = '0.7';

    // Mock axios instance
    mockAxiosInstance = {
      post: jest.fn(),
      get: jest.fn(),
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() }
      }
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    openrouterService = new OpenRouterService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(openrouterService.baseURL).toBe('https://openrouter.ai/api/v1');
      expect(openrouterService.apiKey).toBe('test-api-key');
      expect(openrouterService.defaultModel).toBe('qwen/qwen-2.5-coder-32b-instruct:free');
      expect(openrouterService.useFreeModelsOnly).toBe(true);
    });

    it('should throw error if API key is missing', () => {
      delete process.env.OPENROUTER_API_KEY;
      expect(() => new OpenRouterService()).toThrow('OPENROUTER_API_KEY environment variable is required');
    });

    it('should create axios instance with correct configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://openrouter.ai/api/v1',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://atma.chhrone.web.id',
          'X-Title': 'ATMA - AI Talent Mapping'
        },
        timeout: 45000
      });
    });
  });

  describe('generateResponse', () => {
    const mockMessages = [
      { role: 'user', content: 'Hello, how are you?' }
    ];

    const mockSuccessResponse = {
      data: {
        choices: [{
          message: { content: 'I am doing well, thank you!' },
          finish_reason: 'stop'
        }],
        model: 'qwen/qwen-2.5-coder-32b-instruct:free',
        usage: {
          prompt_tokens: 10,
          completion_tokens: 8,
          total_tokens: 18,
          cost: 0
        }
      },
      headers: {
        'x-request-id': 'test-request-id',
        'x-ratelimit-remaining': '19',
        'x-ratelimit-reset': '**********'
      }
    };

    it('should generate response successfully', async () => {
      mockAxiosInstance.post.mockResolvedValue(mockSuccessResponse);

      const result = await openrouterService.generateResponse(mockMessages);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/chat/completions', {
        model: 'qwen/qwen-2.5-coder-32b-instruct:free',
        messages: mockMessages,
        max_tokens: 1000,
        temperature: 0.7,
        user: undefined,
        usage: { include: true }
      });

      expect(result).toEqual({
        content: 'I am doing well, thank you!',
        model: 'qwen/qwen-2.5-coder-32b-instruct:free',
        usage: {
          prompt_tokens: 10,
          completion_tokens: 8,
          total_tokens: 18,
          cost: 0,
          isFreeModel: true
        },
        processingTime: expect.any(Number),
        finishReason: 'stop',
        metadata: {
          requestId: 'test-request-id',
          provider: undefined,
          rateLimit: {
            remaining: '19',
            reset: '**********'
          }
        }
      });
    });

    it('should handle custom options', async () => {
      mockAxiosInstance.post.mockResolvedValue(mockSuccessResponse);

      const options = {
        model: 'custom-model',
        maxTokens: 500,
        temperature: 0.5,
        userId: 'user-123',
        topP: 0.9,
        frequencyPenalty: 0.1
      };

      await openrouterService.generateResponse(mockMessages, options);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/chat/completions', {
        model: 'custom-model',
        messages: mockMessages,
        max_tokens: 500,
        temperature: 0.5,
        user: 'user-123',
        usage: { include: true },
        top_p: 0.9,
        frequency_penalty: 0.1
      });
    });

    it('should validate messages input', async () => {
      await expect(openrouterService.generateResponse([])).rejects.toThrow('Messages must be a non-empty array');
      await expect(openrouterService.generateResponse(null)).rejects.toThrow('Messages must be a non-empty array');
    });

    it('should handle API errors and trigger fallback', async () => {
      const mockError = new Error('API Error');
      mockAxiosInstance.post
        .mockRejectedValueOnce(mockError)
        .mockResolvedValueOnce(mockSuccessResponse);

      const result = await openrouterService.generateResponse(mockMessages);

      expect(mockAxiosInstance.post).toHaveBeenCalledTimes(2);
      expect(result.model).toBe('qwen/qwen-2.5-coder-32b-instruct:free');
    });
  });

  describe('handleFallback', () => {
    const mockMessages = [{ role: 'user', content: 'Test message' }];
    const mockError = new Error('Primary model failed');

    it('should try fallback model first', async () => {
      const mockSuccessResponse = {
        data: {
          choices: [{ message: { content: 'Fallback response' }, finish_reason: 'stop' }],
          model: 'meta-llama/llama-3.2-3b-instruct:free',
          usage: { prompt_tokens: 5, completion_tokens: 5, total_tokens: 10 }
        },
        headers: {}
      };

      mockAxiosInstance.post.mockResolvedValue(mockSuccessResponse);

      const result = await openrouterService.handleFallback(mockMessages, {}, mockError);

      expect(result.model).toBe('meta-llama/llama-3.2-3b-instruct:free');
    });

    it('should try emergency fallback if free models only is disabled', async () => {
      openrouterService.useFreeModelsOnly = false;
      
      const mockSuccessResponse = {
        data: {
          choices: [{ message: { content: 'Emergency response' }, finish_reason: 'stop' }],
          model: 'openai/gpt-4o-mini',
          usage: { prompt_tokens: 5, completion_tokens: 5, total_tokens: 10, cost: 0.001 }
        },
        headers: {}
      };

      mockAxiosInstance.post
        .mockRejectedValueOnce(mockError)
        .mockResolvedValueOnce(mockSuccessResponse);

      const result = await openrouterService.handleFallback(
        mockMessages, 
        { model: 'meta-llama/llama-3.2-3b-instruct:free', isFirstRetry: true }, 
        mockError
      );

      expect(result.model).toBe('openai/gpt-4o-mini');
    });

    it('should throw error when all fallbacks fail', async () => {
      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(
        openrouterService.handleFallback(
          mockMessages, 
          { isFirstRetry: true, isSecondRetry: true }, 
          mockError
        )
      ).rejects.toThrow('All OpenRouter models failed. Original error: Primary model failed');
    });
  });

  describe('isFreeModel', () => {
    it('should correctly identify free models', () => {
      expect(openrouterService.isFreeModel('qwen/qwen-2.5-coder-32b-instruct:free')).toBe(true);
      expect(openrouterService.isFreeModel('meta-llama/llama-3.2-3b-instruct:free')).toBe(true);
      expect(openrouterService.isFreeModel('microsoft/phi-3-mini-128k-instruct:free')).toBe(true);
      expect(openrouterService.isFreeModel('huggingface/zephyr-7b-beta:free')).toBe(true);
      expect(openrouterService.isFreeModel('any-model:free')).toBe(true);
    });

    it('should correctly identify paid models', () => {
      expect(openrouterService.isFreeModel('openai/gpt-4o-mini')).toBe(false);
      expect(openrouterService.isFreeModel('anthropic/claude-3-haiku')).toBe(false);
      expect(openrouterService.isFreeModel('openai/gpt-3.5-turbo')).toBe(false);
    });
  });

  describe('optimizeConversationHistory', () => {
    it('should return empty array for invalid input', () => {
      expect(openrouterService.optimizeConversationHistory(null)).toEqual([]);
      expect(openrouterService.optimizeConversationHistory(undefined)).toEqual([]);
      expect(openrouterService.optimizeConversationHistory('not-array')).toEqual([]);
    });

    it('should preserve system message', () => {
      const messages = [
        { role: 'system', content: 'You are a helpful assistant' },
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ];

      const result = openrouterService.optimizeConversationHistory(messages, 1000);
      
      expect(result[0]).toEqual({ role: 'system', content: 'You are a helpful assistant' });
      expect(result).toHaveLength(3);
    });

    it('should limit messages based on token count', () => {
      const messages = [
        { role: 'user', content: 'A'.repeat(1000) }, // ~250 tokens
        { role: 'assistant', content: 'B'.repeat(1000) }, // ~250 tokens
        { role: 'user', content: 'C'.repeat(1000) }, // ~250 tokens
        { role: 'assistant', content: 'D'.repeat(1000) }, // ~250 tokens
        { role: 'user', content: 'E'.repeat(1000) } // ~250 tokens
      ];

      const result = openrouterService.optimizeConversationHistory(messages, 500); // ~500 tokens limit
      
      // Should include last 2 messages (E and D) which fit in ~500 tokens
      expect(result).toHaveLength(2);
      expect(result[0].content).toContain('D');
      expect(result[1].content).toContain('E');
    });

    it('should ensure at least last message is included', () => {
      const messages = [
        { role: 'user', content: 'A'.repeat(10000) } // Very long message
      ];

      const result = openrouterService.optimizeConversationHistory(messages, 100); // Small limit
      
      expect(result).toHaveLength(1);
      expect(result[0].content).toContain('A');
    });
  });

  describe('getAvailableModels', () => {
    it('should fetch available models successfully', async () => {
      const mockModelsResponse = {
        data: {
          data: [
            { id: 'model1', name: 'Model 1' },
            { id: 'model2', name: 'Model 2' }
          ]
        }
      };

      mockAxiosInstance.get.mockResolvedValue(mockModelsResponse);

      const result = await openrouterService.getAvailableModels();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/models');
      expect(result).toEqual([
        { id: 'model1', name: 'Model 1' },
        { id: 'model2', name: 'Model 2' }
      ]);
    });

    it('should handle API errors', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('API Error'));

      await expect(openrouterService.getAvailableModels()).rejects.toThrow('Failed to fetch available models');
    });
  });

  describe('getFreeModels', () => {
    it('should filter and return only free models', async () => {
      const mockModels = [
        { 
          id: 'model1:free', 
          pricing: { prompt: "0", completion: "0" } 
        },
        { 
          id: 'model2', 
          pricing: { prompt: "0.001", completion: "0.002" } 
        },
        { 
          id: 'model3:free', 
          pricing: { prompt: "0", completion: "0" } 
        }
      ];

      jest.spyOn(openrouterService, 'getAvailableModels').mockResolvedValue(mockModels);

      const result = await openrouterService.getFreeModels();

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('model1:free');
      expect(result[1].id).toBe('model3:free');
    });
  });
});
