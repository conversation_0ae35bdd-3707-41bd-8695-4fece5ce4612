// Integration test setup
require('dotenv').config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Global test timeout for integration tests
jest.setTimeout(30000);

// Global setup and teardown
beforeAll(async () => {
  // Wait for services to be ready
  await new Promise(resolve => setTimeout(resolve, 5000));
});

afterAll(async () => {
  // Cleanup after all tests
  await new Promise(resolve => setTimeout(resolve, 1000));
});

beforeEach(() => {
  // Reset mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Cleanup after each test
});
