const request = require('supertest');
const express = require('express');
const { 
  freeModelLimiter, 
  messageRateLimiter, 
  burstProtectionLimiter,
  rateLimitLogger 
} = require('../../src/middleware/freeModelRateLimiter');

// Mock logger
jest.mock('../../src/utils/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

describe('Free Model Rate Limiter Middleware', () => {
  let app;

  beforeEach(() => {
    // Reset environment variables
    process.env.FREE_MODEL_RATE_WINDOW_MS = '60000';
    process.env.FREE_MODEL_RATE_LIMIT_PER_MINUTE = '5'; // Low limit for testing
    process.env.MESSAGE_RATE_WINDOW_MS = '300000';
    process.env.MESSAGE_RATE_LIMIT_PER_5MIN = '10';
    process.env.BURST_PROTECTION_WINDOW_MS = '10000';
    process.env.BURST_PROTECTION_LIMIT = '3';

    // Create test app
    app = express();
    app.use(express.json());
    
    // Mock auth middleware
    app.use((req, res, next) => {
      req.user = { id: 'test-user-123', isPremium: false };
      req.ip = '127.0.0.1';
      next();
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('freeModelLimiter', () => {
    beforeEach(() => {
      app.use('/test', freeModelLimiter);
      app.get('/test', (req, res) => {
        res.json({ success: true, message: 'Request successful' });
      });
    });

    it('should allow requests within rate limit', async () => {
      const response = await request(app)
        .get('/test')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.headers['ratelimit-limit']).toBe('5');
      expect(response.headers['ratelimit-remaining']).toBe('4');
    });

    it('should block requests exceeding rate limit', async () => {
      // Make requests up to the limit
      for (let i = 0; i < 5; i++) {
        await request(app).get('/test').expect(200);
      }

      // Next request should be rate limited
      const response = await request(app)
        .get('/test')
        .expect(429);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('FREE_MODEL_RATE_LIMIT_EXCEEDED');
      expect(response.body.error.retryAfter).toBe(60);
    });

    it('should use different keys for different users', async () => {
      // First user makes requests
      for (let i = 0; i < 5; i++) {
        await request(app).get('/test').expect(200);
      }

      // First user is rate limited
      await request(app).get('/test').expect(429);

      // Create app with different user
      const app2 = express();
      app2.use(express.json());
      app2.use((req, res, next) => {
        req.user = { id: 'test-user-456', isPremium: false };
        req.ip = '127.0.0.1';
        next();
      });
      app2.use('/test', freeModelLimiter);
      app2.get('/test', (req, res) => {
        res.json({ success: true, message: 'Request successful' });
      });

      // Second user should not be rate limited
      const response = await request(app2)
        .get('/test')
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should skip rate limiting for premium users', async () => {
      const premiumApp = express();
      premiumApp.use(express.json());
      premiumApp.use((req, res, next) => {
        req.user = { id: 'premium-user-123', isPremium: true };
        req.ip = '127.0.0.1';
        next();
      });
      premiumApp.use('/test', freeModelLimiter);
      premiumApp.get('/test', (req, res) => {
        res.json({ success: true, message: 'Request successful' });
      });

      // Make more requests than the limit
      for (let i = 0; i < 10; i++) {
        const response = await request(premiumApp)
          .get('/test')
          .expect(200);
        expect(response.body.success).toBe(true);
      }
    });

    it('should skip rate limiting for health check endpoints', async () => {
      app.get('/health', freeModelLimiter, (req, res) => {
        res.json({ status: 'healthy' });
      });

      // Make more requests than the limit to health endpoint
      for (let i = 0; i < 10; i++) {
        const response = await request(app)
          .get('/health')
          .expect(200);
        expect(response.body.status).toBe('healthy');
      }
    });

    it('should use IP address when user is not authenticated', async () => {
      const unauthApp = express();
      unauthApp.use(express.json());
      unauthApp.use((req, res, next) => {
        req.ip = '*************';
        next();
      });
      unauthApp.use('/test', freeModelLimiter);
      unauthApp.get('/test', (req, res) => {
        res.json({ success: true, message: 'Request successful' });
      });

      const response = await request(unauthApp)
        .get('/test')
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('messageRateLimiter', () => {
    beforeEach(() => {
      app.use('/messages', messageRateLimiter);
      app.post('/messages', (req, res) => {
        res.json({ success: true, message: 'Message sent' });
      });
      app.get('/messages', (req, res) => {
        res.json({ success: true, messages: [] });
      });
    });

    it('should allow POST requests within rate limit', async () => {
      const response = await request(app)
        .post('/messages')
        .send({ content: 'Test message' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.headers['ratelimit-limit']).toBe('10');
    });

    it('should block POST requests exceeding rate limit', async () => {
      // Make requests up to the limit
      for (let i = 0; i < 10; i++) {
        await request(app)
          .post('/messages')
          .send({ content: `Test message ${i}` })
          .expect(200);
      }

      // Next request should be rate limited
      const response = await request(app)
        .post('/messages')
        .send({ content: 'Test message over limit' })
        .expect(429);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('MESSAGE_RATE_LIMIT_EXCEEDED');
    });

    it('should skip rate limiting for GET requests', async () => {
      // Make many GET requests
      for (let i = 0; i < 15; i++) {
        const response = await request(app)
          .get('/messages')
          .expect(200);
        expect(response.body.success).toBe(true);
      }
    });

    it('should skip rate limiting for premium users', async () => {
      const premiumApp = express();
      premiumApp.use(express.json());
      premiumApp.use((req, res, next) => {
        req.user = { id: 'premium-user-123', isPremium: true };
        req.ip = '127.0.0.1';
        next();
      });
      premiumApp.use('/messages', messageRateLimiter);
      premiumApp.post('/messages', (req, res) => {
        res.json({ success: true, message: 'Message sent' });
      });

      // Make more requests than the limit
      for (let i = 0; i < 15; i++) {
        const response = await request(premiumApp)
          .post('/messages')
          .send({ content: `Test message ${i}` })
          .expect(200);
        expect(response.body.success).toBe(true);
      }
    });
  });

  describe('burstProtectionLimiter', () => {
    beforeEach(() => {
      app.use('/burst-test', burstProtectionLimiter);
      app.post('/burst-test', (req, res) => {
        res.json({ success: true, message: 'Request processed' });
      });
      app.get('/burst-test', (req, res) => {
        res.json({ success: true, message: 'GET request processed' });
      });
    });

    it('should allow requests within burst limit', async () => {
      for (let i = 0; i < 3; i++) {
        const response = await request(app)
          .post('/burst-test')
          .send({ data: `test ${i}` })
          .expect(200);
        expect(response.body.success).toBe(true);
      }
    });

    it('should block requests exceeding burst limit', async () => {
      // Make requests up to the limit
      for (let i = 0; i < 3; i++) {
        await request(app)
          .post('/burst-test')
          .send({ data: `test ${i}` })
          .expect(200);
      }

      // Next request should trigger burst protection
      const response = await request(app)
        .post('/burst-test')
        .send({ data: 'burst test' })
        .expect(429);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('BURST_PROTECTION_TRIGGERED');
      expect(response.body.error.retryAfter).toBe(10);
    });

    it('should skip burst protection for GET requests', async () => {
      // Make many GET requests rapidly
      for (let i = 0; i < 10; i++) {
        const response = await request(app)
          .get('/burst-test')
          .expect(200);
        expect(response.body.success).toBe(true);
      }
    });

    it('should skip burst protection for premium users', async () => {
      const premiumApp = express();
      premiumApp.use(express.json());
      premiumApp.use((req, res, next) => {
        req.user = { id: 'premium-user-123', isPremium: true };
        req.ip = '127.0.0.1';
        next();
      });
      premiumApp.use('/burst-test', burstProtectionLimiter);
      premiumApp.post('/burst-test', (req, res) => {
        res.json({ success: true, message: 'Request processed' });
      });

      // Make more requests than the burst limit
      for (let i = 0; i < 10; i++) {
        const response = await request(premiumApp)
          .post('/burst-test')
          .send({ data: `test ${i}` })
          .expect(200);
        expect(response.body.success).toBe(true);
      }
    });

    it('should skip burst protection for health endpoints', async () => {
      app.get('/health', burstProtectionLimiter, (req, res) => {
        res.json({ status: 'healthy' });
      });

      // Make many requests to health endpoint
      for (let i = 0; i < 10; i++) {
        const response = await request(app)
          .get('/health')
          .expect(200);
        expect(response.body.status).toBe('healthy');
      }
    });
  });

  describe('rateLimitLogger', () => {
    const logger = require('../../src/utils/logger');

    beforeEach(() => {
      app.use('/log-test', rateLimitLogger);
      app.use('/log-test', freeModelLimiter);
      app.get('/log-test', (req, res) => {
        res.json({ success: true });
      });
    });

    it('should log rate limit usage', async () => {
      const response = await request(app)
        .get('/log-test')
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // Wait for response to finish
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(logger.debug).toHaveBeenCalledWith('Rate limit usage', {
        userId: 'test-user-123',
        ip: '127.0.0.1',
        endpoint: '/log-test',
        method: 'GET',
        remaining: '4',
        limit: '5',
        resetTime: expect.any(String),
        statusCode: 200
      });
    });

    it('should not log when no rate limit headers are present', async () => {
      const simpleApp = express();
      simpleApp.use('/simple', rateLimitLogger);
      simpleApp.get('/simple', (req, res) => {
        res.json({ success: true });
      });

      await request(simpleApp)
        .get('/simple')
        .expect(200);

      // Wait for response to finish
      await new Promise(resolve => setTimeout(resolve, 10));

      // Should not log rate limit usage when no headers are present
      expect(logger.debug).not.toHaveBeenCalledWith(
        'Rate limit usage',
        expect.any(Object)
      );
    });
  });

  describe('Combined rate limiters', () => {
    beforeEach(() => {
      app.use('/combined', rateLimitLogger);
      app.use('/combined', burstProtectionLimiter);
      app.use('/combined', freeModelLimiter);
      app.use('/combined', messageRateLimiter);
      app.post('/combined', (req, res) => {
        res.json({ success: true, message: 'All limiters passed' });
      });
    });

    it('should apply all rate limiters in sequence', async () => {
      const response = await request(app)
        .post('/combined')
        .send({ content: 'Test message' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('All limiters passed');
    });

    it('should be blocked by the most restrictive limiter first', async () => {
      // Burst protection has the lowest limit (3), so it should trigger first
      for (let i = 0; i < 3; i++) {
        await request(app)
          .post('/combined')
          .send({ content: `Test message ${i}` })
          .expect(200);
      }

      // Next request should be blocked by burst protection
      const response = await request(app)
        .post('/combined')
        .send({ content: 'Should be blocked' })
        .expect(429);

      expect(response.body.error.code).toBe('BURST_PROTECTION_TRIGGERED');
    });
  });
});
