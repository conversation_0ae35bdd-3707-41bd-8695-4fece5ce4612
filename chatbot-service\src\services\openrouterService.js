const axios = require('axios');
const logger = require('../utils/logger');

/**
 * OpenRouter Service for AI conversation generation
 * Optimized for free models with fallback strategy
 */
class OpenRouterService {
  constructor() {
    this.baseURL = process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1';
    this.apiKey = process.env.OPENROUTER_API_KEY;
    this.defaultModel = process.env.DEFAULT_MODEL || 'qwen/qwen-2.5-coder-32b-instruct:free';
    this.fallbackModel = process.env.FALLBACK_MODEL || 'meta-llama/llama-3.2-3b-instruct:free';
    this.emergencyFallbackModel = process.env.EMERGENCY_FALLBACK_MODEL || 'openai/gpt-4o-mini';
    this.useFreeModelsOnly = process.env.USE_FREE_MODELS_ONLY === 'true';
    this.maxTokens = parseInt(process.env.MAX_TOKENS || '1000');
    this.temperature = parseFloat(process.env.TEMPERATURE || '0.7');
    this.timeout = parseInt(process.env.OPENROUTER_TIMEOUT || '45000');

    if (!this.apiKey) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.HTTP_REFERER || 'https://atma.chhrone.web.id',
        'X-Title': process.env.X_TITLE || 'ATMA - AI Talent Mapping'
      },
      timeout: this.timeout
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('OpenRouter API request', {
          url: config.url,
          model: config.data?.model,
          messageCount: config.data?.messages?.length
        });
        return config;
      },
      (error) => {
        logger.error('OpenRouter API request error', { error: error.message });
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug('OpenRouter API response', {
          status: response.status,
          model: response.data?.model,
          usage: response.data?.usage
        });
        return response;
      },
      (error) => {
        logger.error('OpenRouter API response error', {
          status: error.response?.status,
          message: error.response?.data?.error?.message || error.message,
          code: error.response?.data?.error?.code
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Generate AI response for conversation
   * @param {Array} messages - Conversation history in OpenAI format
   * @param {Object} options - Generation options
   * @returns {Object} AI response with metadata
   */
  async generateResponse(messages, options = {}) {
    const startTime = Date.now();
    
    try {
      const model = options.model || this.defaultModel;
      const isFreeModel = this.isFreeModel(model);

      // Validate messages format
      if (!Array.isArray(messages) || messages.length === 0) {
        throw new Error('Messages must be a non-empty array');
      }

      // Build request payload
      const payload = {
        model: model,
        messages: messages,
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        user: options.userId,
        usage: { include: true }
      };

      // Add optional parameters for supported models
      if (options.topP) payload.top_p = options.topP;
      if (options.frequencyPenalty) payload.frequency_penalty = options.frequencyPenalty;
      if (options.presencePenalty) payload.presence_penalty = options.presencePenalty;
      if (options.stop) payload.stop = options.stop;

      logger.info('Generating AI response', {
        model,
        messageCount: messages.length,
        isFreeModel,
        userId: options.userId
      });

      const response = await this.client.post('/chat/completions', payload);
      const processingTime = Date.now() - startTime;

      // Extract response data
      const choice = response.data.choices[0];
      const usage = response.data.usage || {};

      const result = {
        content: choice.message.content,
        model: response.data.model || model,
        usage: {
          prompt_tokens: usage.prompt_tokens || 0,
          completion_tokens: usage.completion_tokens || 0,
          total_tokens: usage.total_tokens || 0,
          cost: isFreeModel ? 0 : (usage.cost || 0),
          isFreeModel: isFreeModel
        },
        processingTime,
        finishReason: choice.finish_reason,
        metadata: {
          requestId: response.headers['x-request-id'],
          provider: response.data.provider,
          rateLimit: {
            remaining: response.headers['x-ratelimit-remaining'],
            reset: response.headers['x-ratelimit-reset']
          }
        }
      };

      logger.info('AI response generated successfully', {
        model: result.model,
        processingTime,
        tokenUsage: result.usage,
        finishReason: result.finishReason
      });

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.warn('Primary model failed, attempting fallback', {
        model: options.model || this.defaultModel,
        error: error.message,
        processingTime
      });

      return this.handleFallback(messages, options, error);
    }
  }

  /**
   * Handle fallback strategy when primary model fails
   * @param {Array} messages - Conversation history
   * @param {Object} options - Generation options
   * @param {Error} originalError - Original error from primary model
   * @returns {Object} AI response from fallback model
   */
  async handleFallback(messages, options, originalError) {
    const currentModel = options.model || this.defaultModel;

    // First fallback: try free fallback model
    if (currentModel !== this.fallbackModel && !options.isFirstRetry) {
      logger.info('Attempting first fallback', {
        from: currentModel,
        to: this.fallbackModel
      });

      return this.generateResponse(messages, {
        ...options,
        model: this.fallbackModel,
        isFirstRetry: true
      });
    }

    // Second fallback: try emergency paid model (if allowed)
    if (!this.useFreeModelsOnly && 
        currentModel !== this.emergencyFallbackModel && 
        !options.isSecondRetry) {
      
      logger.info('Attempting emergency fallback', {
        from: currentModel,
        to: this.emergencyFallbackModel
      });

      return this.generateResponse(messages, {
        ...options,
        model: this.emergencyFallbackModel,
        isSecondRetry: true
      });
    }

    // All fallbacks failed
    logger.error('All OpenRouter models failed', {
      originalError: originalError.message,
      currentModel,
      useFreeModelsOnly: this.useFreeModelsOnly
    });

    throw new Error(`All OpenRouter models failed. Original error: ${originalError.message}`);
  }

  /**
   * Check if a model is a free model
   * @param {string} model - Model identifier
   * @returns {boolean} True if model is free
   */
  isFreeModel(model) {
    return model.includes(':free') ||
           model === 'qwen/qwen-2.5-coder-32b-instruct:free' ||
           model === 'meta-llama/llama-3.2-3b-instruct:free' ||
           model === 'microsoft/phi-3-mini-128k-instruct:free' ||
           model === 'huggingface/zephyr-7b-beta:free';
  }

  /**
   * Get available models from OpenRouter
   * @returns {Array} List of available models
   */
  async getAvailableModels() {
    try {
      const response = await this.client.get('/models');
      return response.data.data || [];
    } catch (error) {
      logger.error('Failed to fetch available models', { error: error.message });
      throw new Error('Failed to fetch available models');
    }
  }

  /**
   * Get free models only
   * @returns {Array} List of free models
   */
  async getFreeModels() {
    try {
      const models = await this.getAvailableModels();
      return models.filter(model => 
        this.isFreeModel(model.id) && 
        model.pricing?.prompt === "0" && 
        model.pricing?.completion === "0"
      );
    } catch (error) {
      logger.error('Failed to fetch free models', { error: error.message });
      throw new Error('Failed to fetch free models');
    }
  }

  /**
   * Validate conversation history and optimize for token limits
   * @param {Array} messages - Raw conversation history
   * @param {number} maxTokens - Maximum tokens allowed
   * @returns {Array} Optimized message history
   */
  optimizeConversationHistory(messages, maxTokens = 6000) {
    if (!Array.isArray(messages)) {
      return [];
    }

    // Keep system message if present
    const systemMessage = messages.find(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    // Estimate tokens (rough approximation: 1 token ≈ 4 characters)
    const estimateTokens = (text) => Math.ceil(text.length / 4);

    let totalTokens = systemMessage ? estimateTokens(systemMessage.content) : 0;
    const optimizedMessages = systemMessage ? [systemMessage] : [];

    // Add messages from most recent, staying within token limit
    for (let i = conversationMessages.length - 1; i >= 0; i--) {
      const message = conversationMessages[i];
      const messageTokens = estimateTokens(message.content);
      
      if (totalTokens + messageTokens <= maxTokens) {
        optimizedMessages.unshift(message);
        totalTokens += messageTokens;
      } else {
        break;
      }
    }

    // Ensure we have at least the last user message
    if (optimizedMessages.length === (systemMessage ? 1 : 0) && conversationMessages.length > 0) {
      const lastMessage = conversationMessages[conversationMessages.length - 1];
      optimizedMessages.push(lastMessage);
    }

    return optimizedMessages;
  }
}

module.exports = OpenRouterService;
