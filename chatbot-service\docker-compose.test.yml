version: '3.8'

services:
  # Test Database
  postgres-test:
    image: postgres:15-alpine
    container_name: atma-postgres-test
    environment:
      POSTGRES_DB: atma_test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./tests/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d atma_test_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - atma-test-network

  # Redis for rate limiting (test)
  redis-test:
    image: redis:7-alpine
    container_name: atma-redis-test
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - atma-test-network

  # Chatbot Service (Test)
  chatbot-service-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: test
    container_name: atma-chatbot-service-test
    environment:
      NODE_ENV: test
      DB_HOST: postgres-test
      DB_PORT: 5432
      DB_NAME: atma_test_db
      DB_USER: test_user
      DB_PASSWORD: test_password
      DB_SCHEMA: chat
      REDIS_HOST: redis-test
      REDIS_PORT: 6379
      JWT_SECRET: test_jwt_secret_for_testing_only
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY:-test-api-key}
      OPENROUTER_BASE_URL: https://openrouter.ai/api/v1
      DEFAULT_MODEL: qwen/qwen-2.5-coder-32b-instruct:free
      FALLBACK_MODEL: meta-llama/llama-3.2-3b-instruct:free
      USE_FREE_MODELS_ONLY: "true"
      MAX_TOKENS: 1000
      TEMPERATURE: 0.7
      FREE_MODEL_RATE_LIMIT_PER_MINUTE: 5
      MESSAGE_RATE_LIMIT_PER_5MIN: 10
      BURST_PROTECTION_LIMIT: 3
      MAX_MESSAGE_LENGTH: 10000
      LOG_LEVEL: error
      ENABLE_METRICS: "false"
    ports:
      - "3004:3003"
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
    command: npm test
    networks:
      - atma-test-network

  # Integration Test Runner
  integration-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: test
    container_name: atma-integration-tests
    environment:
      NODE_ENV: test
      DB_HOST: postgres-test
      DB_PORT: 5432
      DB_NAME: atma_test_db
      DB_USER: test_user
      DB_PASSWORD: test_password
      DB_SCHEMA: chat
      REDIS_HOST: redis-test
      REDIS_PORT: 6379
      JWT_SECRET: test_jwt_secret_for_testing_only
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY:-test-api-key}
      CHATBOT_SERVICE_URL: http://chatbot-service-test:3003
      LOG_LEVEL: error
    depends_on:
      chatbot-service-test:
        condition: service_started
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run test:integration
    networks:
      - atma-test-network
    profiles:
      - integration

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local

networks:
  atma-test-network:
    driver: bridge
