const request = require('supertest');
const app = require('../../src/app');
const { Conversation, Message, UsageTracking } = require('../../src/models');
const OpenRouterService = require('../../src/services/openrouterService');

// Mock the models
jest.mock('../../src/models', () => ({
  Conversation: {
    findOne: jest.fn()
  },
  Message: {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    findAndCountAll: jest.fn(),
    findByPk: jest.fn()
  },
  UsageTracking: {
    create: jest.fn()
  }
}));

// Mock OpenRouter service
jest.mock('../../src/services/openrouterService');

// Mock logger
jest.mock('../../src/utils/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

// Mock JWT verification
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn(() => ({
    id: 'user-123',
    email: '<EMAIL>',
    user_type: 'user'
  }))
}));

describe('Message Controller', () => {
  let mockOpenRouterService;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock OpenRouter service instance
    mockOpenRouterService = {
      generateResponse: jest.fn(),
      optimizeConversationHistory: jest.fn()
    };
    OpenRouterService.mockImplementation(() => mockOpenRouterService);

    // Set up environment variables
    process.env.JWT_SECRET = 'test-secret';
  });

  describe('POST /conversations/:conversationId/messages', () => {
    const conversationId = 'conv-123';
    const validToken = 'Bearer valid-token';

    const mockConversation = {
      id: conversationId,
      user_id: 'user-123',
      status: 'active',
      update: jest.fn()
    };

    const mockUserMessage = {
      id: 'msg-user-123',
      conversation_id: conversationId,
      sender_type: 'user',
      content: 'Hello, how are you?',
      content_type: 'text',
      created_at: new Date()
    };

    const mockAssistantMessage = {
      id: 'msg-assistant-123',
      conversation_id: conversationId,
      sender_type: 'assistant',
      content: 'I am doing well, thank you!',
      metadata: {
        model: 'qwen/qwen-2.5-coder-32b-instruct:free',
        finish_reason: 'stop',
        processing_time: 1500
      },
      created_at: new Date()
    };

    const mockAIResponse = {
      content: 'I am doing well, thank you!',
      model: 'qwen/qwen-2.5-coder-32b-instruct:free',
      usage: {
        prompt_tokens: 10,
        completion_tokens: 8,
        total_tokens: 18,
        cost: 0,
        isFreeModel: true
      },
      processingTime: 1500,
      finishReason: 'stop',
      metadata: {
        requestId: 'req-123',
        provider: 'openrouter'
      }
    };

    beforeEach(() => {
      Conversation.findOne.mockResolvedValue(mockConversation);
      Message.create
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockAssistantMessage);
      Message.findAll.mockResolvedValue([mockUserMessage]);
      UsageTracking.create.mockResolvedValue({});
      mockOpenRouterService.generateResponse.mockResolvedValue(mockAIResponse);
      mockOpenRouterService.optimizeConversationHistory.mockReturnValue([
        { role: 'user', content: 'Hello, how are you?' }
      ]);
    });

    it('should send message and get AI response successfully', async () => {
      const response = await request(app)
        .post(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .send({
          content: 'Hello, how are you?',
          content_type: 'text'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user_message');
      expect(response.body.data).toHaveProperty('assistant_message');
      expect(response.body.data).toHaveProperty('usage');
      expect(response.body.data).toHaveProperty('processing_time');

      // Verify conversation access check
      expect(Conversation.findOne).toHaveBeenCalledWith({
        where: {
          id: conversationId,
          user_id: 'user-123',
          status: 'active'
        }
      });

      // Verify user message creation
      expect(Message.create).toHaveBeenCalledWith({
        conversation_id: conversationId,
        sender_type: 'user',
        content: 'Hello, how are you?',
        content_type: 'text',
        parent_message_id: undefined
      });

      // Verify AI response generation
      expect(mockOpenRouterService.generateResponse).toHaveBeenCalledWith(
        [{ role: 'user', content: 'Hello, how are you?' }],
        {
          userId: 'user-123',
          conversationId: conversationId
        }
      );

      // Verify assistant message creation
      expect(Message.create).toHaveBeenCalledWith({
        conversation_id: conversationId,
        sender_type: 'assistant',
        content: 'I am doing well, thank you!',
        metadata: {
          model: 'qwen/qwen-2.5-coder-32b-instruct:free',
          finish_reason: 'stop',
          processing_time: 1500,
          request_id: 'req-123',
          provider: 'openrouter'
        }
      });

      // Verify usage tracking
      expect(UsageTracking.create).toHaveBeenCalledWith({
        conversation_id: conversationId,
        message_id: 'msg-assistant-123',
        model_used: 'qwen/qwen-2.5-coder-32b-instruct:free',
        prompt_tokens: 10,
        completion_tokens: 8,
        total_tokens: 18,
        cost_credits: 0,
        is_free_model: true,
        processing_time_ms: 1500
      });

      // Verify conversation update
      expect(mockConversation.update).toHaveBeenCalledWith({
        updated_at: expect.any(Date)
      });
    });

    it('should return 400 for empty message content', async () => {
      const response = await request(app)
        .post(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .send({
          content: '',
          content_type: 'text'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_MESSAGE_CONTENT');
    });

    it('should return 404 for non-existent conversation', async () => {
      Conversation.findOne.mockResolvedValue(null);

      const response = await request(app)
        .post(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .send({
          content: 'Hello, how are you?'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('CONVERSATION_NOT_FOUND');
    });

    it('should return 401 for missing authorization', async () => {
      const response = await request(app)
        .post(`/conversations/${conversationId}/messages`)
        .send({
          content: 'Hello, how are you?'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('UNAUTHORIZED');
    });

    it('should handle OpenRouter rate limit errors', async () => {
      mockOpenRouterService.generateResponse.mockRejectedValue(
        new Error('rate limit exceeded')
      );

      const response = await request(app)
        .post(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .send({
          content: 'Hello, how are you?'
        });

      expect(response.status).toBe(429);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('RATE_LIMIT_EXCEEDED');
    });

    it('should handle OpenRouter service unavailable errors', async () => {
      mockOpenRouterService.generateResponse.mockRejectedValue(
        new Error('All OpenRouter models failed')
      );

      const response = await request(app)
        .post(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .send({
          content: 'Hello, how are you?'
        });

      expect(response.status).toBe(503);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('AI_SERVICE_UNAVAILABLE');
    });
  });

  describe('GET /conversations/:conversationId/messages', () => {
    const conversationId = 'conv-123';
    const validToken = 'Bearer valid-token';

    const mockConversation = {
      id: conversationId,
      user_id: 'user-123',
      status: 'active'
    };

    const mockMessages = [
      {
        id: 'msg-1',
        conversation_id: conversationId,
        sender_type: 'user',
        content: 'Hello',
        created_at: new Date('2024-01-01T10:00:00Z')
      },
      {
        id: 'msg-2',
        conversation_id: conversationId,
        sender_type: 'assistant',
        content: 'Hi there!',
        created_at: new Date('2024-01-01T10:00:01Z')
      }
    ];

    beforeEach(() => {
      Conversation.findOne.mockResolvedValue(mockConversation);
      Message.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockMessages
      });
    });

    it('should get messages successfully', async () => {
      const response = await request(app)
        .get(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.messages).toHaveLength(2);
      expect(response.body.data.conversation_id).toBe(conversationId);
      expect(response.body.data.pagination).toHaveProperty('current_page', 1);
      expect(response.body.data.pagination).toHaveProperty('total_items', 2);
    });

    it('should handle pagination parameters', async () => {
      const response = await request(app)
        .get(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .query({
          page: 2,
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(Message.findAndCountAll).toHaveBeenCalledWith({
        where: { conversation_id: conversationId },
        order: [['created_at', 'ASC']],
        limit: 10,
        offset: 10,
        include: []
      });
    });

    it('should include usage tracking when requested', async () => {
      const response = await request(app)
        .get(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken)
        .query({
          include_usage: 'true'
        });

      expect(response.status).toBe(200);
      expect(Message.findAndCountAll).toHaveBeenCalledWith({
        where: { conversation_id: conversationId },
        order: [['created_at', 'ASC']],
        limit: 50,
        offset: 0,
        include: [{
          model: UsageTracking,
          as: 'usage_tracking',
          required: false
        }]
      });
    });

    it('should return 404 for non-existent conversation', async () => {
      Conversation.findOne.mockResolvedValue(null);

      const response = await request(app)
        .get(`/conversations/${conversationId}/messages`)
        .set('Authorization', validToken);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('CONVERSATION_NOT_FOUND');
    });
  });

  describe('POST /conversations/:conversationId/messages/:messageId/regenerate', () => {
    const conversationId = 'conv-123';
    const messageId = 'msg-123';
    const validToken = 'Bearer valid-token';

    const mockConversation = {
      id: conversationId,
      user_id: 'user-123',
      status: 'active'
    };

    const mockOriginalMessage = {
      id: messageId,
      conversation_id: conversationId,
      sender_type: 'assistant',
      content: 'Original response',
      created_at: new Date('2024-01-01T10:00:01Z'),
      parent_message_id: null
    };

    const mockNewMessage = {
      id: 'msg-new-123',
      conversation_id: conversationId,
      sender_type: 'assistant',
      content: 'Regenerated response',
      metadata: {
        model: 'qwen/qwen-2.5-coder-32b-instruct:free',
        regenerated_from: messageId
      }
    };

    const mockAIResponse = {
      content: 'Regenerated response',
      model: 'qwen/qwen-2.5-coder-32b-instruct:free',
      usage: {
        prompt_tokens: 10,
        completion_tokens: 8,
        total_tokens: 18,
        cost: 0,
        isFreeModel: true
      },
      processingTime: 1200,
      finishReason: 'stop',
      metadata: {
        requestId: 'req-456'
      }
    };

    beforeEach(() => {
      Conversation.findOne.mockResolvedValue(mockConversation);
      Message.findOne.mockResolvedValue(mockOriginalMessage);
      Message.create.mockResolvedValue(mockNewMessage);
      Message.findAll.mockResolvedValue([]);
      UsageTracking.create.mockResolvedValue({});
      mockOpenRouterService.generateResponse.mockResolvedValue(mockAIResponse);
      mockOpenRouterService.optimizeConversationHistory.mockReturnValue([]);
    });

    it('should regenerate response successfully', async () => {
      const response = await request(app)
        .post(`/conversations/${conversationId}/messages/${messageId}/regenerate`)
        .set('Authorization', validToken)
        .send({});

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('original_message_id', messageId);
      expect(response.body.data).toHaveProperty('new_message');
      expect(response.body.data).toHaveProperty('usage');

      // Verify original message lookup
      expect(Message.findOne).toHaveBeenCalledWith({
        where: {
          id: messageId,
          conversation_id: conversationId,
          sender_type: 'assistant'
        }
      });

      // Verify new message creation
      expect(Message.create).toHaveBeenCalledWith({
        conversation_id: conversationId,
        sender_type: 'assistant',
        content: 'Regenerated response',
        parent_message_id: null,
        metadata: {
          model: 'qwen/qwen-2.5-coder-32b-instruct:free',
          finish_reason: 'stop',
          processing_time: 1200,
          request_id: 'req-456',
          provider: undefined,
          regenerated_from: messageId
        }
      });
    });

    it('should return 404 for non-existent message', async () => {
      Message.findOne.mockResolvedValue(null);

      const response = await request(app)
        .post(`/conversations/${conversationId}/messages/${messageId}/regenerate`)
        .set('Authorization', validToken)
        .send({});

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('MESSAGE_NOT_FOUND');
    });

    it('should use custom model when specified', async () => {
      const customModel = 'custom-model';

      const response = await request(app)
        .post(`/conversations/${conversationId}/messages/${messageId}/regenerate`)
        .set('Authorization', validToken)
        .send({
          model: customModel
        });

      expect(response.status).toBe(200);
      expect(mockOpenRouterService.generateResponse).toHaveBeenCalledWith(
        [],
        {
          userId: 'user-123',
          conversationId: conversationId,
          model: customModel
        }
      );
    });
  });
});
