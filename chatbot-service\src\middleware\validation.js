const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Validate request body against schema
 * @param {Object} schema - Joi schema
 * @returns {Function} Express middleware
 */
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorDetails = error.details.map(detail => detail.message);
      
      logger.warn('Request validation failed', {
        path: req.path,
        method: req.method,
        errors: errorDetails,
        requestId: req.id
      });

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: errorDetails
        }
      });
    }

    // Replace request body with validated value
    req.body = value;
    next();
  };
};

/**
 * Validate request query parameters against schema
 * @param {Object} schema - Joi schema
 * @returns {Function} Express middleware
 */
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorDetails = error.details.map(detail => detail.message);
      
      logger.warn('Query validation failed', {
        path: req.path,
        method: req.method,
        errors: errorDetails,
        requestId: req.id
      });

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Query validation failed',
          details: errorDetails
        }
      });
    }

    // Replace request query with validated value
    req.query = value;
    next();
  };
};

// Validation schemas
const schemas = {
  // Create conversation schema
  createConversation: Joi.object({
    title: Joi.string().max(255).optional()
      .messages({
        'string.max': 'Title must be at most 255 characters long'
      }),
    context_type: Joi.string().valid('general', 'assessment', 'career_guidance').optional()
      .messages({
        'any.only': 'Context type must be one of: general, assessment, career_guidance'
      }),
    context_data: Joi.object().optional(),
    metadata: Joi.object().optional()
  }),

  // Update conversation schema
  updateConversation: Joi.object({
    title: Joi.string().max(255).optional()
      .messages({
        'string.max': 'Title must be at most 255 characters long'
      }),
    context_data: Joi.object().optional(),
    metadata: Joi.object().optional(),
    status: Joi.string().valid('active', 'archived').optional()
      .messages({
        'any.only': 'Status must be either active or archived'
      })
  }),

  // Create message schema
  createMessage: Joi.object({
    content: Joi.string().required().max(parseInt(process.env.MAX_MESSAGE_LENGTH || '10000'))
      .messages({
        'string.empty': 'Message content is required',
        'string.max': `Message content must be at most ${process.env.MAX_MESSAGE_LENGTH || '10000'} characters long`,
        'any.required': 'Message content is required'
      }),
    sender_type: Joi.string().valid('user', 'assistant', 'system').required()
      .messages({
        'any.only': 'Sender type must be one of: user, assistant, system',
        'any.required': 'Sender type is required'
      }),
    content_type: Joi.string().valid('text', 'image', 'file').optional().default('text')
      .messages({
        'any.only': 'Content type must be one of: text, image, file'
      }),
    metadata: Joi.object().optional(),
    parent_message_id: Joi.string().uuid().optional()
      .messages({
        'string.guid': 'Parent message ID must be a valid UUID'
      })
  }),

  // Query schemas
  getConversationsQuery: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(20),
    include_archived: Joi.string().valid('true', 'false').optional().default('false'),
    context_type: Joi.string().valid('general', 'assessment', 'career_guidance').optional()
  }),

  getConversationQuery: Joi.object({
    include_messages: Joi.string().valid('true', 'false').optional().default('false'),
    message_limit: Joi.number().integer().min(1).max(200).optional().default(50)
  }),

  getMessagesQuery: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(50)
  })
};

module.exports = {
  validateBody,
  validateQuery,
  schemas
};
