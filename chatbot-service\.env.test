# Test Environment Configuration
NODE_ENV=test
PORT=3003
HOST=0.0.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_test_db
DB_USER=test_user
DB_PASSWORD=test_password
DB_SCHEMA=chat

# JWT Configuration
JWT_SECRET=test_jwt_secret_for_testing_only

# OpenRouter Configuration
OPENROUTER_API_KEY=test-openrouter-api-key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=qwen/qwen-2.5-coder-32b-instruct:free
FALLBACK_MODEL=meta-llama/llama-3.2-3b-instruct:free
EMERGENCY_FALLBACK_MODEL=openai/gpt-4o-mini
USE_FREE_MODELS_ONLY=true
MAX_TOKENS=1000
TEMPERATURE=0.7
OPENROUTER_TIMEOUT=45000

# Rate Limiting Configuration
FREE_MODEL_RATE_WINDOW_MS=60000
FREE_MODEL_RATE_LIMIT_PER_MINUTE=5
MESSAGE_RATE_WINDOW_MS=300000
MESSAGE_RATE_LIMIT_PER_5MIN=10
BURST_PROTECTION_WINDOW_MS=10000
BURST_PROTECTION_LIMIT=3

# Message Configuration
MAX_MESSAGE_LENGTH=10000
MAX_CONVERSATION_HISTORY_TOKENS=6000

# Logging Configuration
LOG_LEVEL=error

# Disable features for testing
ENABLE_METRICS=false
ENABLE_REQUEST_LOGGING=false
ENABLE_QUERY_LOGGING=false
ENABLE_DEBUG_LOGS=false
