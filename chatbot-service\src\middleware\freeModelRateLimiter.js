const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

/**
 * Rate limiter specifically designed for free model usage
 * More restrictive than general API rate limiting
 */
const freeModelLimiter = rateLimit({
  windowMs: parseInt(process.env.FREE_MODEL_RATE_WINDOW_MS || '60000'), // 1 minute default
  max: parseInt(process.env.FREE_MODEL_RATE_LIMIT_PER_MINUTE || '20'), // 20 requests per minute
  message: {
    success: false,
    error: {
      code: 'FREE_MODEL_RATE_LIMIT_EXCEEDED',
      message: 'Free model rate limit exceeded. Please wait before sending another message.',
      retryAfter: '1 minute',
      details: 'Free models have stricter rate limits to ensure fair usage for all users.'
    }
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  
  // Custom key generator to identify users
  keyGenerator: (req) => {
    // Use user ID if authenticated, otherwise fall back to IP
    const userId = req.user?.id;
    const ip = req.ip || req.connection.remoteAddress;
    
    if (userId) {
      return `free_model_user_${userId}`;
    }
    return `free_model_ip_${ip}`;
  },

  // Custom handler for rate limit exceeded
  handler: (req, res) => {
    const userId = req.user?.id;
    const ip = req.ip;
    
    logger.warn('Free model rate limit exceeded', {
      userId,
      ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      method: req.method
    });

    res.status(429).json({
      success: false,
      error: {
        code: 'FREE_MODEL_RATE_LIMIT_EXCEEDED',
        message: 'Free model rate limit exceeded. Please wait before sending another message.',
        retryAfter: Math.ceil(parseInt(process.env.FREE_MODEL_RATE_WINDOW_MS || '60000') / 1000),
        details: 'Free models have stricter rate limits to ensure fair usage for all users.',
        timestamp: new Date().toISOString()
      }
    });
  },

  // Skip rate limiting for certain conditions
  skip: (req) => {
    // Skip rate limiting if user has premium access (if implemented)
    if (req.user?.isPremium) {
      return true;
    }

    // Skip for health checks and other non-message endpoints
    if (req.path.includes('/health') || req.path.includes('/status')) {
      return true;
    }

    return false;
  },

  // Store rate limit data in memory (consider Redis for production)
  store: undefined // Uses default memory store
});

/**
 * Enhanced rate limiter for message sending specifically
 * Even more restrictive for actual AI generation
 */
const messageRateLimiter = rateLimit({
  windowMs: parseInt(process.env.MESSAGE_RATE_WINDOW_MS || '300000'), // 5 minutes default
  max: parseInt(process.env.MESSAGE_RATE_LIMIT_PER_5MIN || '50'), // 50 messages per 5 minutes
  message: {
    success: false,
    error: {
      code: 'MESSAGE_RATE_LIMIT_EXCEEDED',
      message: 'Message rate limit exceeded. Please wait before sending more messages.',
      retryAfter: '5 minutes',
      details: 'This limit helps ensure system stability and fair usage.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  keyGenerator: (req) => {
    const userId = req.user?.id;
    const ip = req.ip || req.connection.remoteAddress;
    
    if (userId) {
      return `message_user_${userId}`;
    }
    return `message_ip_${ip}`;
  },

  handler: (req, res) => {
    const userId = req.user?.id;
    const ip = req.ip;
    
    logger.warn('Message rate limit exceeded', {
      userId,
      ip,
      userAgent: req.get('User-Agent'),
      conversationId: req.params.conversationId,
      endpoint: req.originalUrl
    });

    res.status(429).json({
      success: false,
      error: {
        code: 'MESSAGE_RATE_LIMIT_EXCEEDED',
        message: 'Message rate limit exceeded. Please wait before sending more messages.',
        retryAfter: Math.ceil(parseInt(process.env.MESSAGE_RATE_WINDOW_MS || '300000') / 1000),
        details: 'This limit helps ensure system stability and fair usage.',
        timestamp: new Date().toISOString()
      }
    });
  },

  skip: (req) => {
    // Skip for premium users
    if (req.user?.isPremium) {
      return true;
    }

    // Skip for GET requests (reading messages)
    if (req.method === 'GET') {
      return true;
    }

    return false;
  }
});

/**
 * Burst protection for rapid successive requests
 * Very short window, very low limit
 */
const burstProtectionLimiter = rateLimit({
  windowMs: parseInt(process.env.BURST_PROTECTION_WINDOW_MS || '10000'), // 10 seconds
  max: parseInt(process.env.BURST_PROTECTION_LIMIT || '5'), // 5 requests per 10 seconds
  message: {
    success: false,
    error: {
      code: 'BURST_PROTECTION_TRIGGERED',
      message: 'Too many requests in a short time. Please slow down.',
      retryAfter: '10 seconds',
      details: 'This protection prevents system abuse and ensures stability.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  
  keyGenerator: (req) => {
    const userId = req.user?.id;
    const ip = req.ip || req.connection.remoteAddress;
    
    if (userId) {
      return `burst_user_${userId}`;
    }
    return `burst_ip_${ip}`;
  },

  handler: (req, res) => {
    const userId = req.user?.id;
    const ip = req.ip;
    
    logger.warn('Burst protection triggered', {
      userId,
      ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      method: req.method
    });

    res.status(429).json({
      success: false,
      error: {
        code: 'BURST_PROTECTION_TRIGGERED',
        message: 'Too many requests in a short time. Please slow down.',
        retryAfter: Math.ceil(parseInt(process.env.BURST_PROTECTION_WINDOW_MS || '10000') / 1000),
        details: 'This protection prevents system abuse and ensures stability.',
        timestamp: new Date().toISOString()
      }
    });
  },

  skip: (req) => {
    // Skip for premium users
    if (req.user?.isPremium) {
      return true;
    }

    // Skip for health checks
    if (req.path.includes('/health') || req.path.includes('/status')) {
      return true;
    }

    // Skip for GET requests
    if (req.method === 'GET') {
      return true;
    }

    return false;
  }
});

/**
 * Middleware to log rate limit usage
 */
const rateLimitLogger = (req, res, next) => {
  // Log rate limit headers if present
  res.on('finish', () => {
    const rateLimitRemaining = res.get('RateLimit-Remaining');
    const rateLimitLimit = res.get('RateLimit-Limit');
    const rateLimitReset = res.get('RateLimit-Reset');

    if (rateLimitRemaining !== undefined) {
      logger.debug('Rate limit usage', {
        userId: req.user?.id,
        ip: req.ip,
        endpoint: req.originalUrl,
        method: req.method,
        remaining: rateLimitRemaining,
        limit: rateLimitLimit,
        resetTime: rateLimitReset,
        statusCode: res.statusCode
      });
    }
  });

  next();
};

module.exports = {
  freeModelLimiter,
  messageRateLimiter,
  burstProtectionLimiter,
  rateLimitLogger
};
